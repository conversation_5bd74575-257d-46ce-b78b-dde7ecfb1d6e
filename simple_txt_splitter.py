#!/usr/bin/env python3
"""
简化版TXT文件切分脚本
不依赖外部库，纯Python实现
"""

import os
import json
import re
from datetime import datetime

def split_text_by_length(text, max_length=800, overlap=50):
    """
    按长度切分文本，保持句子完整性
    """
    sentences = re.split(r'[。！？\n]', text)
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # 如果单个句子就超过最大长度，强制切分
        if len(sentence) > max_length:
            # 先保存当前块
            if current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = ""
            
            # 按字符强制切分长句子
            for i in range(0, len(sentence), max_length - overlap):
                chunk_part = sentence[i:i + max_length - overlap]
                chunks.append(chunk_part)
            continue
        
        # 检查添加这个句子后是否超过长度限制
        test_chunk = current_chunk + sentence + "。"
        if len(test_chunk) > max_length and current_chunk:
            # 保存当前块，开始新块
            chunks.append(current_chunk.strip())
            current_chunk = sentence + "。"
        else:
            current_chunk = test_chunk
    
    # 添加最后一个块
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    return [chunk for chunk in chunks if len(chunk.strip()) > 10]  # 过滤太短的块

def add_headers_to_text(text, base_name):
    """
    为文本添加标题结构
    """
    lines = text.split('\n')
    structured_content = f"# {base_name}\n\n"
    
    current_section = ""
    line_count = 0
    section_count = 0
    
    for line in lines:
        line = line.strip()
        if line:
            current_section += line + "\n"
            line_count += 1
            
            # 每30行创建一个新的二级标题
            if line_count % 30 == 0:
                section_count += 1
                structured_content += f"## 第{section_count}节\n\n{current_section}\n"
                current_section = ""
    
    # 添加剩余内容
    if current_section:
        section_count += 1
        structured_content += f"## 第{section_count}节\n\n{current_section}\n"
    
    return structured_content

def split_txt_file(txt_file_path, output_dir, max_chunk_len=800):
    """
    切分单个txt文件
    """
    print(f"开始切分文件: {txt_file_path}")
    
    # 创建输出目录
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    file_output_dir = os.path.join(output_dir, base_name)
    os.makedirs(file_output_dir, exist_ok=True)
    
    try:
        # 读取文件内容
        with open(txt_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字符")
        
        # 添加标题结构
        structured_content = add_headers_to_text(content, base_name)
        
        # 切分文本
        chunks = split_text_by_length(structured_content, max_chunk_len)
        
        # 保存切分结果
        results = []
        for i, chunk in enumerate(chunks):
            chunk_info = {
                'chunk_id': i + 1,
                'content': chunk,
                'length': len(chunk),
                'metadata': {
                    'source_file': txt_file_path,
                    'chunk_index': i,
                    'total_chunks': len(chunks)
                }
            }
            results.append(chunk_info)
            
            # 保存单个chunk到文件
            chunk_file = os.path.join(file_output_dir, f"chunk_{i+1:03d}.txt")
            with open(chunk_file, 'w', encoding='utf-8') as f:
                f.write(chunk)
        
        # 保存元数据
        metadata = {
            'source_file': txt_file_path,
            'split_time': datetime.now().isoformat(),
            'total_chunks': len(chunks),
            'max_chunk_length': max_chunk_len,
            'original_length': len(content),
            'structured_length': len(structured_content),
            'summary': {
                'avg_chunk_length': sum(len(chunk) for chunk in chunks) // len(chunks) if chunks else 0,
                'min_chunk_length': min(len(chunk) for chunk in chunks) if chunks else 0,
                'max_chunk_length': max(len(chunk) for chunk in chunks) if chunks else 0
            }
        }
        
        metadata_file = os.path.join(file_output_dir, "metadata.json")
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # 创建切片索引文件
        index_content = f"# {base_name} 切片索引\n\n"
        index_content += f"- 原文件: {txt_file_path}\n"
        index_content += f"- 切分时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        index_content += f"- 总块数: {len(chunks)}\n"
        index_content += f"- 最大块长度: {max_chunk_len}\n\n"
        index_content += "## 切片列表\n\n"
        
        for i, chunk_info in enumerate(results):
            preview = chunk_info['content'][:100].replace('\n', ' ') + "..."
            index_content += f"{i+1:03d}. **chunk_{i+1:03d}.txt** ({chunk_info['length']} 字符)\n"
            index_content += f"     预览: {preview}\n\n"
        
        index_file = os.path.join(file_output_dir, "README.md")
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"切分完成! 生成了 {len(chunks)} 个块")
        print(f"平均块长度: {metadata['summary']['avg_chunk_length']} 字符")
        print(f"结果保存在: {file_output_dir}")
        
        return results
        
    except Exception as e:
        print(f"切分文件 {txt_file_path} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    # 创建输出目录
    output_dir = "text_chunks"
    os.makedirs(output_dir, exist_ok=True)
    
    # 要切分的txt文件列表
    txt_files = [
        "test.txt",
        "monitor_data/monitor_data_20250806.txt",
        "doc/md_text_split/md_text_split/parsed_example_pdf.txt"
    ]
    
    print("开始批量切分txt文件...")
    print("=" * 50)
    
    total_files = 0
    success_files = 0
    
    # 逐个切分文件
    for txt_file in txt_files:
        if os.path.exists(txt_file):
            print(f"\n正在处理: {txt_file}")
            total_files += 1
            results = split_txt_file(txt_file, output_dir, max_chunk_len=800)
            if results:
                print(f"✓ {txt_file} 切分成功")
                success_files += 1
            else:
                print(f"✗ {txt_file} 切分失败")
        else:
            print(f"⚠ 文件不存在: {txt_file}")
    
    print("\n" + "=" * 50)
    print(f"批量切分完成! 成功处理 {success_files}/{total_files} 个文件")
    print(f"所有切分结果保存在目录: {output_dir}")
    
    # 创建总体索引
    create_master_index(output_dir)

def create_master_index(output_dir):
    """创建总体索引文件"""
    index_content = "# 文本切分总索引\n\n"
    index_content += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    subdirs = []
    for item in os.listdir(output_dir):
        item_path = os.path.join(output_dir, item)
        if os.path.isdir(item_path):
            subdirs.append(item)
    
    if subdirs:
        index_content += "## 切分结果目录\n\n"
        for subdir in sorted(subdirs):
            subdir_path = os.path.join(output_dir, subdir)
            metadata_file = os.path.join(subdir_path, "metadata.json")
            
            if os.path.exists(metadata_file):
                try:
                    with open(metadata_file, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    index_content += f"### {subdir}\n"
                    index_content += f"- 原文件: `{metadata['source_file']}`\n"
                    index_content += f"- 切片数量: {metadata['total_chunks']}\n"
                    index_content += f"- 最大块长度: {metadata['max_chunk_length']}\n"
                    index_content += f"- 平均块长度: {metadata['summary']['avg_chunk_length']}\n"
                    index_content += f"- 目录: `{subdir}/`\n\n"
                except Exception as e:
                    index_content += f"### {subdir}\n"
                    index_content += f"- 状态: 元数据读取失败 ({e})\n\n"
            else:
                index_content += f"### {subdir}\n"
                index_content += f"- 状态: 无元数据文件\n\n"
    
    master_index_file = os.path.join(output_dir, "INDEX.md")
    with open(master_index_file, 'w', encoding='utf-8') as f:
        f.write(index_content)
    
    print(f"总索引文件已创建: {master_index_file}")

if __name__ == "__main__":
    main()
