"""
文档解析工具
用于将文件转换为Markdown格式的API客户端
"""

import requests
import time
import sys
import os
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from utils.http_util import post_no_proxy
# 确保代理配置生效
try:
    from proxy_config import configure_proxy_bypass
    configure_proxy_bypass()
except ImportError:
    pass  # 如果没有代理配置文件，忽略错误


class DocumentParser:
    """文档解析器类"""
    
    def __init__(self, app_code: str = "d25d52877fde483bbae65fbc5465375f"):
        self.app_code = app_code
        self.base_url = "https://icosg.dt.zte.com.cn/zte-igpt-documentparsing"
        self.headers = {"appcode": app_code}
        # 默认不走代理
        self.proxies = {"http": None, "https": None}
    
    def parse_file(
        self, 
        file_path: str, 
        get_image: bool = True,
        use_llm_pdf: bool = False,
        dewater: bool = False,
        dewater_type: str = 'speed',
        poll_interval: int = 2
    ) -> str:
        """
        解析文件并返回Markdown内容
        
        Args:
            file_path: 文件路径
            get_image: 是否获取图片
            use_llm_pdf: 是否使用LLM处理PDF
            dewater: 是否去水印
            dewater_type: 去水印类型
            poll_interval: 轮询间隔（秒）
            
        Returns:
            解析后的Markdown内容
            
        Raises:
            FileNotFoundError: 文件不存在
            Exception: API调用失败或解析错误
        """
        # 验证文件存在
        if not Path(file_path).exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 发送解析请求
        task_id = self._send_parse_request(
            file_path, get_image, use_llm_pdf, dewater, dewater_type
        )
        
        # 轮询获取结果
        return self._poll_for_result(task_id, poll_interval)
    
    def _send_parse_request(
        self, 
        file_path: str, 
        get_image: bool,
        use_llm_pdf: bool,
        dewater: bool,
        dewater_type: str
    ) -> str:
        """发送文件解析请求"""
        url = f"{self.base_url}/file_to_markdown"
        
        data = {
            'use_llm_pdf': use_llm_pdf,
            'dewater': dewater,
            'dewater_type': dewater_type,
            'get_image': get_image
        }
        
        try:
            with open(file_path, 'rb') as file:
                files = {'file': file}
                response = post_no_proxy(url, files=files, headers=self.headers, data=data)
                response.raise_for_status()
                
                result = response.json()
                return result['bo']['task_id']
                
        except requests.RequestException as e:
            raise Exception(f"发送解析请求失败: {e}")
        except KeyError:
            raise Exception("响应格式错误，未找到task_id")
    
    def _poll_for_result(self, task_id: str, poll_interval: int) -> str:
        """轮询获取解析结果"""
        while True:
            response_data = self._get_task_status(task_id)
            status = response_data['bo'][task_id]['status']
            
            if status == 'completed':
                return response_data['bo'][task_id]['content']
            elif status == 'error':
                error_info = response_data['bo'][task_id].get('error_info', '未知错误')
                raise Exception(f"文档解析失败: {error_info}")
            else:
                time.sleep(poll_interval)
    
    def _get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        url = f"{self.base_url}/task-status"
        data = {'task_id': task_id}
        
        try:
            response = post_no_proxy(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise Exception(f"获取任务状态失败: {e}")



def file_parse(file_path: str, get_image: bool = True) -> str:
    """
    解析文件的便捷函数
    
    Args:
        file_path: 文件路径
        get_image: 是否获取图片
        
    Returns:
        解析后的Markdown内容
    """
    parser = DocumentParser()
    return parser.parse_file(file_path, get_image=get_image)


def save_content_to_txt(content: str, output_path: str = None) -> str:
    """
    保存解析内容到txt文件
    
    Args:
        content: 解析后的内容
        output_path: 输出文件路径，如果不提供则自动生成
        
    Returns:
        保存的文件路径
    """
    if output_path is None:
        # 自动生成文件名，使用时间戳
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"parsed_document_{timestamp}.txt"
    
    # 确保输出目录存在
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存内容到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return str(output_file.absolute())


if __name__ == "__main__":
    # 使用示例 - 同时处理PDF和DOC文件
    parser = DocumentParser()
    processed_files = []
    
    # 定义要处理的文件列表
    files_to_process = [
        {"path": "example.docx", "type": "DOCX", "output": "parsed_example_docx.txt"},
        {"path": "example.pdf", "type": "PDF", "output": "parsed_example_pdf.txt"}
    ]
    
    print("=== 批量文档解析工具 ===")
    print("正在检查待解析文件...")
    
    for file_info in files_to_process:
        file_path = Path(file_info["path"])
        if file_path.exists():
            print(f"\n✓ 找到文件: {file_path} ({file_info['type']})")
            try:
                print(f"  正在解析 {file_info['type']} 文件...")
                
                if file_info["type"] == "PDF":
                    # PDF文件使用特殊参数
                    content = parser.parse_file(str(file_path), get_image=False, use_llm_pdf=True)
                else:
                    # DOC/DOCX文件使用标准参数
                    content = parser.parse_file(str(file_path), get_image=True)
                
                # 保存到txt文件
                output_file = save_content_to_txt(content, file_info["output"])
                processed_files.append({
                    "source": str(file_path),
                    "output": output_file,
                    "type": file_info["type"],
                    "size": len(content)
                })
                
                print(f"  ✓ 解析成功!")
                print(f"  📁 输出文件: {file_info['output']}")
                print(f"  📊 内容长度: {len(content):,} 字符")
                print(f"  📝 内容预览: {content[:150].strip()}...")
                
            except Exception as e:
                print(f"  ✗ 解析失败: {e}")
        else:
            print(f"✗ 未找到文件: {file_path}")
    
    # 输出处理结果摘要
    print(f"\n=== 处理完成 ===")
    if processed_files:
        print(f"成功处理 {len(processed_files)} 个文件:")
        for file_info in processed_files:
            print(f"  • {file_info['type']}: {file_info['source']} → {Path(file_info['output']).name}")
            print(f"    内容长度: {file_info['size']:,} 字符")
    else:
        print("未处理任何文件")
        print("请确保以下文件存在于当前目录:")
        for file_info in files_to_process:
            print(f"  • {file_info['path']} ({file_info['type']})")
        print("\n或者修改代码中的文件路径")