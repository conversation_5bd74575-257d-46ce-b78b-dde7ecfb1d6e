#!/usr/bin/env python3
"""
TXT文件切分脚本
使用text_split.py中的MarkdownSplitter来切分txt文件
"""

import os
import sys
import json
from datetime import datetime

# 导入text_split模块
sys.path.append('./doc/md_text_split/md_text_split')
from text_split import MarkdownSplitter

def convert_txt_to_md(txt_file_path, output_dir):
    """
    将txt文件转换为临时md文件，以便使用MarkdownSplitter处理
    """
    # 读取txt文件
    with open(txt_file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 为内容添加标题结构，便于切分
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    
    # 简单的结构化处理：将内容按段落分组并添加标题
    lines = content.split('\n')
    structured_content = f"# {base_name}\n\n"
    
    current_section = ""
    line_count = 0
    
    for line in lines:
        line = line.strip()
        if line:
            current_section += line + "\n"
            line_count += 1
            
            # 每50行创建一个新的二级标题
            if line_count % 50 == 0:
                structured_content += f"\n## 第{line_count//50}部分\n\n{current_section}\n"
                current_section = ""
    
    # 添加剩余内容
    if current_section:
        section_num = (line_count // 50) + 1
        structured_content += f"\n## 第{section_num}部分\n\n{current_section}\n"
    
    # 保存为临时md文件
    temp_md_file = os.path.join(output_dir, f"{base_name}_temp.md")
    with open(temp_md_file, 'w', encoding='utf-8') as f:
        f.write(structured_content)
    
    return temp_md_file

def split_txt_file(txt_file_path, output_dir, max_chunk_len=800):
    """
    切分单个txt文件
    """
    print(f"开始切分文件: {txt_file_path}")
    
    # 创建输出目录
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    file_output_dir = os.path.join(output_dir, base_name)
    os.makedirs(file_output_dir, exist_ok=True)
    
    try:
        # 转换为临时md文件
        temp_md_file = convert_txt_to_md(txt_file_path, file_output_dir)
        
        # 使用MarkdownSplitter切分
        splitter = MarkdownSplitter(pic_temp_path=os.path.join(file_output_dir, "images"))
        chunks = splitter.split_file(temp_md_file, headers_level=6, max_chunk_len=max_chunk_len)
        
        # 保存切分结果
        results = []
        for i, chunk in enumerate(chunks):
            chunk_info = {
                'chunk_id': i + 1,
                'content': chunk['content'],
                'metadata': chunk.get('metadata', {}),
                'images': chunk.get('pict', []),
                'supplement': chunk.get('supplement', '')
            }
            results.append(chunk_info)
            
            # 保存单个chunk到文件
            chunk_file = os.path.join(file_output_dir, f"chunk_{i+1:03d}.txt")
            with open(chunk_file, 'w', encoding='utf-8') as f:
                f.write(chunk['content'])
        
        # 保存元数据
        metadata = {
            'source_file': txt_file_path,
            'split_time': datetime.now().isoformat(),
            'total_chunks': len(chunks),
            'max_chunk_length': max_chunk_len,
            'chunks': results
        }
        
        metadata_file = os.path.join(file_output_dir, "metadata.json")
        with open(metadata_file, 'w', encoding='utf-8', ensure_ascii=False) as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # 删除临时文件
        os.remove(temp_md_file)
        
        print(f"切分完成! 生成了 {len(chunks)} 个块")
        print(f"结果保存在: {file_output_dir}")
        
        return results
        
    except Exception as e:
        print(f"切分文件 {txt_file_path} 时出错: {e}")
        return None

def main():
    """主函数"""
    # 创建输出目录
    output_dir = "text_chunks"
    os.makedirs(output_dir, exist_ok=True)
    
    # 要切分的txt文件列表
    txt_files = [
        "test.txt",
        "monitor_data/monitor_data_20250806.txt",
        "doc/md_text_split/md_text_split/parsed_example_pdf.txt"
    ]
    
    print("开始批量切分txt文件...")
    print("=" * 50)
    
    # 逐个切分文件
    for txt_file in txt_files:
        if os.path.exists(txt_file):
            print(f"\n正在处理: {txt_file}")
            results = split_txt_file(txt_file, output_dir, max_chunk_len=800)
            if results:
                print(f"✓ {txt_file} 切分成功")
            else:
                print(f"✗ {txt_file} 切分失败")
        else:
            print(f"⚠ 文件不存在: {txt_file}")
    
    print("\n" + "=" * 50)
    print("批量切分完成!")
    print(f"所有切分结果保存在目录: {output_dir}")

if __name__ == "__main__":
    main()
